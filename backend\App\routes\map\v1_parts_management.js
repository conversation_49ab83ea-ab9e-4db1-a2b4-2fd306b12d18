const prefix = '/v1/parts_management'; // for org type customer

module.exports = ({ AUTH_MIDDLEWARE }) => {
    return [
        {
            path: prefix + '/products',
            route_file: './routes/parts_management/v1_products',
            middleware: [AUTH_MIDDLEWARE],
        },
        {
            path: prefix + '/spares',
            route_file: './routes/parts_management/v1_spares',
            middleware: [AUTH_MIDDLEWARE],
        },
        {
            path: prefix + '/warehouse',
            route_file: './routes/parts_management/v1_warehouse',
            middleware: [AUTH_MIDDLEWARE],
        },
        {
            path: prefix + '/stocks',
            route_file: './routes/parts_management/v1_stocks',
            middleware: [AUTH_MIDDLEWARE],
        },
        {
            path: prefix + '/stock-transfer',
            route_file: './routes/parts_management/v1_stock_transfer',
            middleware: [AUTH_MIDDLEWARE],
        },
        {
            path: prefix + '/stock-transfer-history',
            route_file: './routes/parts_management/v1_stock_transfer_history',
            middleware: [AUTH_MIDDLEWARE],
        },
        {
            path: prefix + '/sp-inventory-master',
            route_file: './routes/parts_management/v1_sp_inventory_master',
            middleware: [AUTH_MIDDLEWARE],
        },
        {
            path: prefix + '/my-inventory',
            route_file: './routes/parts_management/v1_my_inventory',
            middleware: [AUTH_MIDDLEWARE],
        },
        {
            path: prefix + '/category',
            route_file: './routes/parts_management/v1_category',
            middleware: [AUTH_MIDDLEWARE],
        },
    ];
};
