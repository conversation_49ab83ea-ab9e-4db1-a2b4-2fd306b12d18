import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
    Col,
    Row,
    Tabs,
    Drawer,
    Select,
    Form,
    Button,
    Switch,
    Modal,
    Rate,
    List,
    Table,
    Menu,
    Tag,
    Tooltip,
} from 'antd';
import http_utils from '../../../../util/http_utils';
import CircularProgress from '../../../../components/CircularProgress';
import { SideBar } from './sideBar';
//import { filters } from './filters';

import {
    getAnyObjectFrFilter,
    convertUTCToDisplayTime,
    NoData,
    getLinktoObject,
    handleFilterClearDateIfNull,
    getCreatedDateFilter,
    getDataForLastThreeMonths,
    getRatingFrFilter,
    getViewModeMetaFrFormMeta,
    isScreenZoomPercentage125,
} from '../../../../util/helpers';
import CustomScrollbars from '../../../../util/CustomScrollbars';
import PagedApiListView from '../../../../components/wify-utils/crud/overview/PagedApiListView';
import { getFilterFormMetaFilledWithApiData } from '../../../../components/wify-utils/crud/overview/filter_helpers';
import { Link } from 'react-router-dom';
import moment from 'moment';
import AppModuleHeader from '../../../../components/AppModuleHeader';
import { DownloadOutlined, EditFilled } from '@ant-design/icons';
import ItemEditor from './itemEditor';
import ConfigHelpers from '../../../../util/ConfigHelpers';

const overviewProto_url = '/parts_management/category/overview_proto';
const dataUrl = '/parts_management/category/list';
const isMobileView = window.innerWidth <= 768;

const ManageCategory = () => {
    const formRef = useRef(null);

    const [activeFilters, setActiveFilters] = useState(getFiltersFrmSearch());
    const [visible, setVisible] = useState(false);
    const [drawerState, setDrawerState] = useState(false);
    const [showEditor, setShowEditor] = useState(false);
    const [showItemEditor, setShowItemEditor] = useState(false);
    const [isFormSubmitting, setIsFormSubmitting] = useState(false);
    const [renderHelper, setRenderHelper] = useState(false);
    const [viewData, setViewData] = useState(undefined);
    const [isLoadingViewData, setIsLoadingViewData] = useState(false);
    const [searchFilter, setSearchFilter] = useState(getSearchFromUrl());
    const [error, setError] = useState('');
    const [pagination, setPagination] = useState({ current: 1, pageSize: 10 });
    const [itemEditorData, setItemEditorData] = useState(undefined);

    // === Utility functions moved inside functional scope ===
    function getFiltersFrmSearch() {
        let parsedFilter = new URLSearchParams(window.location.search).get(
            'filters'
        );
        return parsedFilter ? JSON.parse(parsedFilter) : {};
    }

    function getSearchFromUrl() {
        let searchFrmLink = new URLSearchParams(window.location.search).get(
            'query'
        );
        return searchFrmLink ? searchFrmLink : '';
    }

    const initViewData = (
        (forFilterRefresh = false) => {
            if (
                forFilterRefresh ||
                (viewData === undefined && !isLoadingViewData)
            ) {
                setIsLoadingViewData(forFilterRefresh ? false : true);

                var params = {};
                const onComplete = (resp) => {
                    setIsLoadingViewData(false);
                    setViewData(resp.data);
                    setError('');
                };
                const onError = (error) => {
                    setIsLoadingViewData(false);
                    setError(http_utils.decodeErrorToMessage(error));
                };
                http_utils.performGetCall(
                    overviewProto_url,
                    params,
                    onComplete,
                    onError
                );
            }
        }
       
    );

    useEffect(() => {
        initViewData();
    }, [initViewData]);

    const handleFilterChange = (newFilterObject) => {
        setActiveFilters({
            ...newFilterObject,
        });
    };

    const getFilters = () => {
        var filters = [];
        var staticFilters = [...filters]||[];
         
        var defaultAnyMeta = getAnyObjectFrFilter();
        var filtersFrmViewData = viewData?.filters_proto;

        return getFilterFormMetaFilledWithApiData(
            staticFilters,
            defaultAnyMeta,
            filtersFrmViewData
        );
    };

    const onToggleDrawer = () => {
        setDrawerState(!drawerState);
    };

    const getSortFieldMetaFilters = () => {
        //  return [...filters];
        return [];
    };

    const getSearchbarPlaceholder = () => {
        return 'Search by category name';
    };

    const handleSearchChange = (query) => {
        setSearchFilter(query);
    };

    const refresh = () => {
        setRenderHelper(!renderHelper);
    };

    const onEditClick = (data, record) => {
        setItemEditorData(record);
        setShowItemEditor(true);
    };

    const handleOnCloseAddProductModel = () => {
        setShowEditor(false);
    };

    const handleOnCloseItemEditorModel = () => {
        setItemEditorData(undefined);
        setShowItemEditor(false);
    };

    const resetFilter = () => {
        setActiveFilters({});
        setSearchFilter('');
        initViewData(true);
    };

    const notifyDatasetChanged = (entry_id, checkEditorItem = true) => {
        setActiveFilters({ ...activeFilters });
        initViewData(true);
    };

    const getColMeta = () => {
        let rightColFixed = isMobileView ? 'none' : 'right';
        return [
            {
                key: 'category_name',
                dataIndex: 'category_name',
                label: 'Category Name',
                title: 'Category Name',
                width: 275,
                viewMode: true,
            },
           
            {
                key: 'status',
                dataIndex: 'status',
                title: 'Status',
                label: 'Status',
                viewMode: true,
                render: (text, record) => (
                    <Switch
                        checkedChildren="Active"
                        unCheckedChildren="Inactive"
                        checked={record.status === 'Active'}
                    />
                ),
            },
            {
                key: 'action',
                dataIndex: 'action',
                label: 'Action',
                title: 'Action',
                align: 'center',
                fixed: rightColFixed,
                width: 100,
                viewMode: true,
                render: (text, record) => (
                    <Button
                        onClick={() => onEditClick(text, record)}
                        size="small"
                        icon={<EditFilled />}
                        className="gx-mr-1 gx-mb-0 pastel_outline_black"
                    />
                ),
            },
        ];
    };

    const configFrPagedApiListView = () => {
        return {
            dataSourceApi: dataUrl,
            columns: getColMeta(),
        };
    };

    return (
        <>
            {isLoadingViewData ? (
                <div className="gx-loader-view gx-loader-position">
                    <CircularProgress />
                </div>
            ) : viewData === undefined ? (
                <p className="gx-text-red">{error}</p>
            ) : (
                <div className="gx-main-content">
                    <div className="gx-app-module">
                        <div className="gx-d-block gx-d-lg-none">
                            <Drawer
                                placement="left"
                                closable={false}
                                visible={drawerState}
                                onClose={onToggleDrawer}
                            >
                                <SideBar
                                    filters={getFilters()}
                                    onAddProductClick={() =>
                                        setShowEditor(true)
                                    }
                                    onFilterChange={handleFilterChange}
                                    activeFilters={activeFilters}
                                    staticFilters={getSortFieldMetaFilters()}
                                />
                            </Drawer>
                        </div>
                        <div className="gx-module-sidenav gx-d-none gx-d-lg-flex">
                            <SideBar
                                filters={getFilters()}
                                onAddProductClick={() => setShowEditor(true)}
                                onFilterChange={handleFilterChange}
                                activeFilters={activeFilters}
                                staticFilters={getSortFieldMetaFilters()}
                            />
                        </div>
                        {showEditor && (
                            <ItemEditor
                                showEditor={showEditor}
                                onClose={handleOnCloseAddProductModel}
                                onChange={notifyDatasetChanged}
                                configData={viewData?.config_data}
                            />
                        )}
                        <div className="gx-module-box">
                            <div className="gx-module-box-header">
                                <span className="gx-drawer-btn gx-d-flex gx-d-lg-none">
                                    <i
                                        className="icon icon-filter gx-icon-btn"
                                        aria-label="Menu"
                                        onClick={onToggleDrawer}
                                    />
                                </span>
                                <AppModuleHeader
                                    placeholder={getSearchbarPlaceholder()}
                                    currValue={searchFilter}
                                    onChange={handleSearchChange}
                                    showTabularViewOption
                                    tellParentToRefreshList={refresh}
                                />
                            </div>
                            <div className="gx-module-box-content gx-px-3 gx-py-3">
                                <CustomScrollbars>
                                    <div className="wy-table-last-child-width-adjust">
                                        <PagedApiListView
                                            {...configFrPagedApiListView()}
                                            filterObject={activeFilters}
                                            searchQuery={searchFilter}
                                            overflowScrollbar={{
                                                y: isScreenZoomPercentage125()
                                                    ? 340
                                                    : 550,
                                            }}
                                            columns={getColMeta()}
                                            tableView={true}
                                        />
                                    </div>
                                    {showItemEditor && itemEditorData && (
                                        <ItemEditor
                                            itemEditorData={itemEditorData}
                                            showEditor={showItemEditor}
                                            editMode
                                            onClose={
                                                handleOnCloseItemEditorModel
                                            }
                                            onChange={notifyDatasetChanged}
                                            configData={viewData?.config_data}
                                            readOnly={
                                                !ConfigHelpers.getManageInventoryRights().update()
                                            }
                                        />
                                    )}
                                </CustomScrollbars>
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </>
    );
};

export default ManageCategory;
