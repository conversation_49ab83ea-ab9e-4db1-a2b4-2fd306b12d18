import React, { useState, useEffect, useCallback } from 'react';
import { Button } from 'antd';
import CustomScrollbars from '../../../../util/CustomScrollbars';
import QuickFilters from '../../../../components/wify-utils/crud/overview/QuickFilters';
import ConfigHelpers from '../../../../util/ConfigHelpers';

export const SideBar = ({
    activeFilters: propsActiveFilters,
    srvcDetails,
    filters,
    staticFilters,
    onFilterChange,
    onAddProductClick,
}) => {
    const [activeFilters, setActiveFilters] = useState(propsActiveFilters);
    const [serviceTypeDetails, setServiceTypeDetails] = useState(srvcDetails);

    // keep local state in sync with props
    useEffect(() => {
        setActiveFilters(propsActiveFilters);
    }, [propsActiveFilters]);

    useEffect(() => {
        setServiceTypeDetails(srvcDetails);
    }, [srvcDetails]);

    const handleFilterChange = (
        (newFilterObject) => {
            const updatedFilters = {
                ...newFilterObject,
            };
            setActiveFilters(updatedFilters);
            onFilterChange(updatedFilters);
        },
        [onFilterChange]
    );
console.log('called->:',onAddProductClick);
    return (
        <div className="gx-module-side">
            <div className="gx-module-side-header">
                <div className="gx-module-logo">
                    <i className="icon wy-page-icon wy-icon-manage-sku gx-mr-2" />
                    <span>Manage Category</span>
                </div>
            </div>

            <div className="gx-module-side-content">
                <CustomScrollbars className="gx-module-side-scroll">
                    {ConfigHelpers.getManageInventoryRights().create() ? (
                        <div className="gx-module-add-task gx-pb-2">
                            <Button
                                className="gx-btn-block ant-btn"
                                type="primary"
                                aria-label="add"
                                onClick={onAddProductClick}
                            >
                                <i className="icon icon-add-circle gx-mr-2" />
                                <span>Add Category</span>
                            </Button>
                        </div>
                    ) : (
                        <div className="gx-my-3"></div>
                    )}

                    <QuickFilters
                        filters={filters}
                        onFilterChange={handleFilterChange}
                        linkMode
                        activeFilters={activeFilters}
                        staticFilters={staticFilters}
                    />
                </CustomScrollbars>
            </div>
        </div>
    );
};
